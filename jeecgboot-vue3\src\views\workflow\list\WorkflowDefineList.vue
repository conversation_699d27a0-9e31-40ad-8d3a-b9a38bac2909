<template>
  <div class="p-2">
    <!--查询区域-->
    <div class="jeecg-basic-table-form-container">
      <a-form ref="formRef" @keyup.enter="searchQuery" :model="queryParam" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
        <a-row :gutter="48">
          <a-col :span="8">
            <a-form-item name="name">
              <template #label><span title="工作流名称">工作流名</span></template>
              <a-input placeholder="请输入工作流名称" v-model:value="queryParam.name" allow-clear />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item name="businessKey">
              <template #label><span title="业务key">业务key</span></template>
              <a-input placeholder="请输入业务key" v-model:value="queryParam.businessKey" allow-clear />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <span style="float: right; overflow: hidden" class="table-page-search-submitButtons">
              <a-button preIcon="ant-design:reload-outlined" @click="searchReset" style="margin-left: 8px">重置</a-button>
              <a-button type="primary" preIcon="ant-design:search-outlined" @click="reload" style="margin-left: 8px">查询</a-button>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <!--引用表格-->
    <BasicTable @register="registerTable" :rowSelection="rowSelection">
      <!--插槽:table标题-->
      <template #tableTitle>
        <a-button type="primary" v-auth="'workflow:define:add'" @click="handleAdd" preIcon="ant-design:plus-outlined"> 新增 </a-button>
        <a-button type="default" v-auth="'workflow:define:add'" @click="batchHandleDelete" preIcon="ant-design:minus-outlined"> 删除 </a-button>
      </template>
      <!--操作栏-->
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)" />
      </template>
    </BasicTable>
    <!-- 表单区域 -->
    <WorkflowDefineModal ref="registerModal" @success="handleSuccess" />
  </div>
</template>

<script lang="ts" name="workflow-workflowDefine" setup>
  import { ref, reactive } from 'vue';
  import { BasicTable, TableAction } from '/@/components/Table';
  import { useListPage } from '/@/hooks/system/useListPage';
  import { columns } from './WorkflowDefine.data';
  import { list, batchDelete, getImportUrl, getExportUrl } from './WorkflowDefine.api';
  import WorkflowDefineModal from './components/WorkflowDefineModal.vue';
  import { useUserStore } from '/@/store/modules/user';
  import { useGo } from '@/hooks/web/usePage';

  const go = useGo();

  const formRef = ref();
  const queryParam = reactive<any>({});
  const registerModal = ref();
  const userStore = useUserStore();
  //注册table数据
  const { tableContext } = useListPage({
    tableProps: {
      title: '工作流定义',
      api: list,
      columns,
      canResize: false,
      useSearchForm: false,
      actionColumn: {
        title: '操作',
        width: 210,
        fixed: 'right',
      },
      tableSetting: {
        redo: false,
        size: false,
        setting: false,
      },
      beforeFetch: async (params) => {
        return Object.assign(params, queryParam);
      },
    },
  });
  const [registerTable, { reload }, { rowSelection, selectedRowKeys }] = tableContext;

  /**
   * 新增事件
   */
  function handleAdd() {
    registerModal.value.disableSubmit = false;
    registerModal.value.add();
  }

  /**
   * 编辑事件
   */
  function handleEdit(record: Recordable) {
    registerModal.value.disableSubmit = false;
    registerModal.value.edit(record);
  }

  function handleEditProcess(record: Recordable) {
    go('/workflow/define/id/' + record.id);
  }

  /**
   * 详情
   */
  function handleDetail(record: Recordable) {
    registerModal.value.disableSubmit = true;
    registerModal.value.edit(record);
  }

  /**
   * 批量删除事件
   */
  async function batchHandleDelete() {
    await batchDelete({ ids: selectedRowKeys.value }, handleSuccess);
  }

  /**
   * 成功回调
   */
  function handleSuccess() {
    (selectedRowKeys.value = []) && reload();
  }

  /**
   * 操作栏
   */
  function getTableAction(record) {
    return [
      {
        label: '查看',
        onClick: handleDetail.bind(null, record),
      },
      {
        label: '编辑',
        onClick: handleEdit.bind(null, record),
        auth: 'workflow:define:edit',
      },
      {
        label: '流程画布',
        onClick: handleEditProcess.bind(null, record),
        auth: 'workflow:define:edit',
      },
    ];
  }

  /**
   * 查询
   */
  function searchQuery() {
    reload();
  }

  /**
   * 重置
   */
  function searchReset() {
    formRef.value.resetFields();
    selectedRowKeys.value = [];
    //刷新数据
    reload();
  }
</script>

<style lang="less" scoped>
  .jeecg-basic-table-form-container {
    padding: 0;

    .table-page-search-submitButtons {
      display: block;
      margin-bottom: 24px;
      white-space: nowrap;
    }

    .query-group-cust {
      min-width: 100px !important;
    }

    .query-group-split-cust {
      width: 30px;
      display: inline-block;
      text-align: center;
    }

    .ant-form-item:not(.ant-form-item-with-help) {
      margin-bottom: 16px;
      height: 32px;
    }

    :deep(.ant-picker),
    :deep(.ant-input-number) {
      width: 100%;
    }
  }
</style>
